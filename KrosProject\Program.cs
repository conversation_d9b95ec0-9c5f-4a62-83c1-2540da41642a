using System;

class Program
{
    static void Main(string[] args)
    {
        Console.WriteLine("Sorting Algorithms Demo");
        Console.WriteLine("======================");

        int[] originalArray = { 64, 34, 25, 12, 22, 11, 90 };
        Console.WriteLine($"Original array: [{string.Join(", ", originalArray)}]");
        Console.WriteLine();

        // Test Bubble Sort
        int[] bubbleArray = (int[])originalArray.Clone();
        BubbleSort(bubbleArray);
        Console.WriteLine($"Bubble Sort:    [{string.Join(", ", bubbleArray)}]");

        // Test Selection Sort
        int[] selectionArray = (int[])originalArray.Clone();
        SelectionSort(selectionArray);
        Console.WriteLine($"Selection Sort: [{string.Join(", ", selectionArray)}]");

        // Test Insertion Sort
        int[] insertionArray = (int[])originalArray.Clone();
        InsertionSort(insertionArray);
        Console.WriteLine($"Insertion Sort: [{string.Join(", ", insertionArray)}]");

        // Test Quick Sort
        int[] quickArray = (int[])originalArray.Clone();
        QuickSort(quickArray, 0, quickArray.Length - 1);
        Console.WriteLine($"Quick Sort:     [{string.Join(", ", quickArray)}]");

        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }

    static void BubbleSort(int[] array)
    {
        int n = array.Length;
        for (int i = 0; i < n - 1; i++)
        {
            for (int j = 0; j < n - i - 1; j++)
            {
                if (array[j] > array[j + 1])
                {
                    // Swap
                    int temp = array[j];
                    array[j] = array[j + 1];
                    array[j + 1] = temp;
                }
            }
        }
    }

    static void SelectionSort(int[] array)
    {
        int n = array.Length;
        for (int i = 0; i < n - 1; i++)
        {
            int minIndex = i;
            for (int j = i + 1; j < n; j++)
            {
                if (array[j] < array[minIndex])
                {
                    minIndex = j;
                }
            }
            // Swap
            int temp = array[i];
            array[i] = array[minIndex];
            array[minIndex] = temp;
        }
    }

    static void InsertionSort(int[] array)
    {
        int n = array.Length;
        for (int i = 1; i < n; i++)
        {
            int key = array[i];
            int j = i - 1;
            while (j >= 0 && array[j] > key)
            {
                array[j + 1] = array[j];
                j--;
            }
            array[j + 1] = key;
        }
    }

    static void QuickSort(int[] array, int left, int right)
    {
        if (left < right)
        {
            int pivotIndex = Partition(array, left, right);
            QuickSort(array, left, pivotIndex - 1);
            QuickSort(array, pivotIndex + 1, right);
        }
    }

    static int Partition(int[] array, int left, int right)
    {
        int pivot = array[right];
        int i = left - 1;

        for (int j = left; j < right; j++)
        {
            if (array[j] < pivot)
            {
                i++;
                // Swap
                int temp = array[i];
                array[i] = array[j];
                array[j] = temp;
            }
        }

        // Swap
        int temp = array[i + 1];
        array[i + 1] = array[right];
        array[right] = temp;

        return i + 1;
    }
}
